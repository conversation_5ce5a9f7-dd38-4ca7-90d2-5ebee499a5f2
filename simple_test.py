# -*- coding: utf-8 -*-
"""
简单测试导入
"""

try:
    print("测试配置模块...")
    from crawler.config import get_config
    config = get_config()
    print(f"✓ 配置加载成功: 线程数={config['threads']}")
    
    print("测试代理模块...")
    from crawler.proxy import ProxyManager
    print("✓ 代理模块导入成功")
    
    print("测试Cookie模块...")
    from crawler.cookies import GoogleCookie
    print("✓ Cookie模块导入成功")
    
    print("测试爬虫模块...")
    from crawler.spider import GoogleSearch
    print("✓ 爬虫模块导入成功")
    
    print("🎉 所有模块导入成功！")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
