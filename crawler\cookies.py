# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON> Time: 2025/3/19 17:12
File Name:cookies.py
"""
import asyncio
import random
import logging
import json
import os
import threading
import time
from datetime import datetime, timedelta
from playwright.async_api import async_playwright
from crawler.proxy import ProxyManager
from crawler.config import get_cookie_config, get_proxy_config

if not os.path.exists('cookies'):
    os.makedirs('cookies')


class HumanBehaviorSimulator:
    """人类行为模拟工具类"""

    @staticmethod
    def get_random_viewport():
        """生成随机设备视口"""
        return {
            "width": random.choice([1920, 1366, 1536, 1440]),
            "height": random.choice([1080, 768, 864, 900])
        }

    @staticmethod
    async def simulate_typing(element, text):
        """模拟人类输入行为"""
        for char in text:
            await element.type(char)
            await asyncio.sleep(random.uniform(0.08, 0.15))
            if random.random() < 0.07:
                await element.press("Backspace")
                await element.type(char)

    @staticmethod
    async def random_mouse_movement(page):
        """生成随机鼠标轨迹"""
        for _ in range(random.randint(3, 5)):
            x = random.randint(0, page.viewport_size['width'])
            y = random.randint(0, page.viewport_size['height'])
            await page.mouse.move(x, y, steps=random.randint(2, 5))
            await asyncio.sleep(random.uniform(0.3, 0.7))

    @staticmethod
    async def simulate_scroll(page):
        """模拟自然滚动"""
        scroll_distance = random.randint(300, 800)
        await page.mouse.wheel(0, scroll_distance)
        await asyncio.sleep(random.uniform(0.5, 1.2))

    @staticmethod
    async def random_page_interaction(page):
        """随机页面交互"""
        actions = [
            lambda: page.mouse.wheel(0, random.randint(200, 500)),
            lambda: HumanBehaviorSimulator.random_mouse_movement(page),
            lambda: page.keyboard.press('PageDown'),
            lambda: asyncio.sleep(random.uniform(0.5, 1.5))
        ]
        for _ in range(random.randint(2, 4)):
            await random.choice(actions)()
            await asyncio.sleep(random.uniform(0.3, 0.8))


class GoogleCookie:
    """谷歌Cookie类，合并了管理和获取功能"""

    def __init__(self, cookie_file="google_cookies.json", ):
        self.cookies = {}
        self.cookie_file = cookie_file
        self.human_simulator = HumanBehaviorSimulator()
        self.config = {
            'timeout': 30000,
            'headless': True
        }
        self.load_cookies_from_file()

    def load_cookies_from_file(self):
        """从文件加载cookies"""
        if os.path.exists(self.cookie_file):
            try:
                with open(self.cookie_file, 'r') as f:
                    self.cookies = json.load(f)
                if self.cookies == {}:
                    self.create_cookies()
                logging.info("Cookies loaded from file")
            except:
                logging.warning("Failed to load cookies from file")
                self.cookies = {}

    def save_cookies_to_file(self):
        """保存cookies到文件"""
        with open(self.cookie_file, 'w') as f:
            json.dump(self.cookies, f)
        logging.info("Cookies saved to file")

    def get_cookies(self):
        """获取cookies"""
        return self.cookies

    def update_cookies(self, new_cookies):
        """更新cookies"""
        self.cookies = new_cookies
        self.save_cookies_to_file()

    def is_cookies_valid(self):
        """检查cookies是否有效"""
        if not self.cookies:
            return False

        # 简单检查cookie是否包含必要的字段
        required_cookies = ['AEC', 'NID']
        for cookie in required_cookies:
            if cookie not in self.cookies:
                return False

        return True

    async def _detect_captcha(self, page):
        """检测是否出现验证码"""
        captcha_selectors = [
            'form#captcha-form',
            'div.g-recaptcha',
            'input[name="captcha"]',
            'title:has-text("unusual traffic")'
        ]

        for selector in captcha_selectors:
            if await page.query_selector(selector):
                logging.warning("检测到验证码!")
                return True
        return False

    async def _perform_search(self, page, query):
        """执行搜索操作"""
        logging.info(f"正在执行搜索操作: {query}")
        await page.goto('https://www.google.com', timeout=self.config.get('timeout', 30000))
        await page.wait_for_load_state('networkidle')

        search_box = await page.wait_for_selector('textarea[name="q"]', timeout=15000)
        await HumanBehaviorSimulator.simulate_typing(search_box, query)
        await asyncio.sleep(random.uniform(0.8, 1.3))
        await search_box.press('Enter')

        await page.wait_for_selector('div#search', timeout=20000)
        return not await self._detect_captcha(page)

    async def fetch_new_cookies(self, search_query="python programming"):
        """获取谷歌搜索页面的cookies"""
        async with async_playwright() as p:
            # 检查代理配置
            proxy_config = get_proxy_config()
            if proxy_config["enabled"]:
                logging.info("代理已启用，获取代理用于Cookie创建")
                proxy_manager = ProxyManager(num=2, minute=5)
                proxy = proxy_manager.get_random_proxy()  # ip:port
                if proxy and ':' in proxy:
                    proxy_server = {'server': f'http://{proxy}'}
                    logging.info(f"使用代理创建Cookie: {proxy}")
                else:
                    proxy_server = None
                    logging.warning("代理已启用但无可用代理，直接连接创建Cookie")
            else:
                proxy_server = None
                logging.info("代理已禁用，直接连接创建Cookie")
            # 随机选择一个浏览器UA
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ]
            # 浏览器启动选项
            browser_args = [
                '--disable-blink-features=AutomationControlled',
                '--disable-features=IsolateOrigins,site-per-process',
                '--no-sandbox'
            ]

            # 浏览器配置
            browser_options = {
                'headless': self.config.get('headless', True),
                'args': browser_args,
                "ignore_default_args": ['--enable-automation']
            }
            logging.info(f"使用代理服务器: {proxy_server}  获取cookies")
            browser = await p.chromium.launch(**browser_options, proxy=proxy_server, timeout=60000)

            # 创建上下文
            viewport = self.human_simulator.get_random_viewport()
            context = await browser.new_context(
                viewport=viewport,
                user_agent=random.choice(user_agents)
            )

            # 禁用WebDriver
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });

                // 隐藏自动化特征
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({state: Notification.permission}) :
                        originalQuery(parameters)
                );

                // 模拟插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [installer.txt, 2, 3, 4, 5].map(() => ({
                        0: {
                            type: "application/x-google-chrome-pdf",
                            suffixes: "pdf",
                            description: "Portable Document Format"
                        },
                        name: "Chrome PDF Plugin",
                        filename: "internal-pdf-viewer",
                        description: "Portable Document Format",
                        length: installer.txt
                    }))
                });
            """)

            # 创建新页面
            page = await context.new_page()

            try:
                # 执行搜索操作
                search_success = await self._perform_search(page, search_query)

                if not search_success:
                    logging.warning("搜索操作失败，可能被检测为机器人")
                    return {}

                # 模拟随机互动
                await self.human_simulator.random_page_interaction(page)

                # 获取cookies
                cookies = await context.cookies()
                formatted_cookies = {}

                # 格式化cookies为字典
                for cookie in cookies:
                    if cookie.get('domain', '').endswith('google.com'):
                        formatted_cookies[cookie['name']] = cookie['value']

                # 更新cookie
                logging.info(f"获取新的cookies: {formatted_cookies}")
                self.update_cookies(formatted_cookies)
                logging.info("Cookies fetched successfully!")

                # 返回cookies
                return formatted_cookies

            except Exception as e:
                logging.error(f"Error fetching cookies: {e}")
                return {}

            finally:
                await browser.close()

    def create_cookies(self):
        """获取有效的cookies，如果无效则重新获取"""
        # 随机生成搜索词，使获取cookie的过程更自然
        search_queries = [
            "what is LLM",
            "news headlines",
            "best restaurants near me",
            "how to learn python",
            "upcoming movies",
            "stock market news",
            "healthy recipes",
            "travel destinations",
            "latest technology trends",
            "home workout routines",
            "best restaurants in town",
            "best coffee shops in the city",
            "popular music tracks",
            "best places to visit in Europe",
            "best restaurants in my city",
            "best restaurants in my country",
            "best restaurants in my stat",
            "chinese food",
            "japanese food",
            "italian food",
            "what is popmart",
            "how to learn python",
            "how to learn japanese",
            "how to learn chinese",
            "LLM",
            "agentic rag",
            "llm agent",
            "what is KG",
            "graphrag",
            "lightrag"
        ]

        # 获取新的cookies
        cookies = asyncio.run(self.fetch_new_cookies(random.choice(search_queries)))
        return cookies


async def main():
    cookie_handler = GoogleCookie()

    # 获取cookies，如果现有cookie无效则自动获取新cookie
    cookies = cookie_handler.get_cookies()
    if not cookie_handler.is_cookies_valid():
        logging.info("现有cookie无效，正在获取新cookie...")
        cookies = await cookie_handler.fetch_new_cookies()

    logging.info("当前cookie:", cookies)


class CookieFileManager:
    """Cookie文件管理类"""
    def __init__(self, cookie_id):
        self.cookie_id = cookie_id
        self.cookie_file = f"cookies/pool_cookie_{cookie_id}.json"
        self.meta_file = f"cookies/pool_cookie_{cookie_id}_meta.json"
        self.in_use = False
        self.last_used = datetime.now()

        # 加载或初始化元数据
        self._load_meta()

    def _load_meta(self):
        """加载元数据（失败次数等）"""
        try:
            if os.path.exists(self.meta_file):
                with open(self.meta_file, 'r') as f:
                    meta = json.load(f)
                    self.failure_count = meta.get('failure_count', 0)
                    self.created_time = meta.get('created_time', datetime.now().isoformat())
            else:
                self.failure_count = 0
                self.created_time = datetime.now().isoformat()
                self._save_meta()
        except Exception as e:
            logging.error(f"加载Cookie {self.cookie_id} 元数据失败: {e}")
            self.failure_count = 0
            self.created_time = datetime.now().isoformat()

    def _save_meta(self):
        """保存元数据"""
        try:
            meta = {
                'failure_count': self.failure_count,
                'created_time': self.created_time,
                'last_used': self.last_used.isoformat()
            }
            with open(self.meta_file, 'w') as f:
                json.dump(meta, f, indent=2)
        except Exception as e:
            logging.error(f"保存Cookie {self.cookie_id} 元数据失败: {e}")

    def load_cookies(self):
        """加载Cookie内容"""
        try:
            if os.path.exists(self.cookie_file):
                with open(self.cookie_file, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logging.error(f"加载Cookie {self.cookie_id} 内容失败: {e}")
            return {}

    def save_cookies(self, cookies):
        """保存Cookie内容"""
        try:
            with open(self.cookie_file, 'w') as f:
                json.dump(cookies, f, indent=2)
        except Exception as e:
            logging.error(f"保存Cookie {self.cookie_id} 内容失败: {e}")

    def mark_failure(self):
        """标记失败"""
        self.failure_count += 1
        self.last_used = datetime.now()
        self._save_meta()
        logging.info(f"Cookie {self.cookie_id} 失败次数: {self.failure_count}")

    def mark_success(self):
        """标记成功"""
        self.failure_count = 0
        self.last_used = datetime.now()
        self._save_meta()

    def is_expired(self, max_failures):
        """检查是否已过期（失败次数过多）"""
        return self.failure_count >= max_failures

    def delete_files(self):
        """删除Cookie文件和元数据文件"""
        try:
            if os.path.exists(self.cookie_file):
                os.remove(self.cookie_file)
                logging.info(f"删除Cookie文件: {self.cookie_file}")
            if os.path.exists(self.meta_file):
                os.remove(self.meta_file)
                logging.info(f"删除元数据文件: {self.meta_file}")
        except Exception as e:
            logging.error(f"删除Cookie {self.cookie_id} 文件失败: {e}")


class CookiePool:
    """Cookie池管理类 - 基于文件的固定数量Cookie池"""

    def __init__(self):
        self.config = get_cookie_config()
        self.pool_size = self.config["pool_size"]
        self.max_failures = self.config["max_failures"]
        self.refresh_interval = self.config["refresh_interval"]

        # 固定数量的Cookie文件管理器
        self.cookie_managers = {}  # {cookie_id: CookieFileManager}
        self.lock = threading.Lock()

        # 初始化固定数量的Cookie文件
        self._initialize_pool()

        # 启动后台刷新线程
        self.refresh_thread = threading.Thread(target=self._refresh_worker, daemon=True)
        self.refresh_thread.start()

        logging.info(f"Cookie池初始化完成，固定大小: {self.pool_size}")

    def _initialize_pool(self):
        """初始化固定数量的Cookie文件"""
        logging.info(f"正在初始化{self.pool_size}个Cookie文件...")

        for i in range(self.pool_size):
            cookie_manager = CookieFileManager(i)
            self.cookie_managers[i] = cookie_manager

            # 检查Cookie文件是否存在，不存在则创建
            cookies = cookie_manager.load_cookies()
            if not cookies:
                logging.info(f"Cookie文件 {i} 不存在或为空，创建新Cookie...")
                self._create_new_cookie(i)
            else:
                logging.info(f"Cookie文件 {i} 已存在，失败次数: {cookie_manager.failure_count}")

        logging.info(f"Cookie池初始化完成，固定大小: {len(self.cookie_managers)}")

    def get_cookie(self):
        """获取一个可用的Cookie"""
        with self.lock:
            # 查找可用的Cookie（未在使用且未过期）
            available_managers = [
                manager for manager in self.cookie_managers.values()
                if not manager.in_use and not manager.is_expired(self.max_failures)
            ]

            if not available_managers:
                logging.warning("没有可用的Cookie")
                return None

            # 选择最少使用的Cookie
            cookie_manager = min(available_managers, key=lambda x: x.last_used)
            cookie_manager.in_use = True

            # 加载Cookie内容
            cookies = cookie_manager.load_cookies()
            if not cookies:
                logging.warning(f"Cookie {cookie_manager.cookie_id} 文件为空，尝试重新创建")
                cookie_manager.in_use = False
                self._create_new_cookie(cookie_manager.cookie_id)
                return None

            logging.debug(f"分配Cookie {cookie_manager.cookie_id}，失败次数: {cookie_manager.failure_count}")
            return cookie_manager, cookies

    def release_cookie(self, cookie_manager, success=True):
        """释放Cookie"""
        with self.lock:
            if cookie_manager.cookie_id in self.cookie_managers:
                cookie_manager.in_use = False
                if success:
                    cookie_manager.mark_success()
                else:
                    cookie_manager.mark_failure()

                logging.debug(f"释放Cookie {cookie_manager.cookie_id}，成功: {success}，失败次数: {cookie_manager.failure_count}")

                # 如果Cookie失败次数过多，删除文件并重新创建
                if cookie_manager.is_expired(self.max_failures):
                    logging.info(f"Cookie {cookie_manager.cookie_id} 失败次数达到{self.max_failures}次，删除并重新创建")
                    cookie_manager.delete_files()

                    # 异步重新创建Cookie
                    threading.Thread(target=self._create_new_cookie, args=(cookie_manager.cookie_id,), daemon=True).start()

    def _create_new_cookie(self, cookie_id):
        """为指定ID创建新Cookie"""
        try:
            logging.info(f"开始为Cookie {cookie_id} 创建新Cookie...")
            cookie_handler = GoogleCookie(cookie_file=f"cookies/pool_cookie_{cookie_id}.json")
            cookies = cookie_handler.create_cookies()

            if cookies:
                with self.lock:
                    # 重新创建CookieFileManager
                    new_manager = CookieFileManager(cookie_id)
                    new_manager.save_cookies(cookies)
                    new_manager.failure_count = 0  # 重置失败次数
                    new_manager._save_meta()
                    self.cookie_managers[cookie_id] = new_manager
                    logging.info(f"成功为Cookie {cookie_id} 创建新Cookie")
            else:
                logging.error(f"为Cookie {cookie_id} 创建新Cookie失败")
        except Exception as e:
            logging.error(f"为Cookie {cookie_id} 创建新Cookie时出错: {e}")

    def _refresh_worker(self):
        """后台刷新工作线程"""
        while True:
            try:
                time.sleep(self.refresh_interval)
                self._refresh_expired_cookies()
            except Exception as e:
                logging.error(f"刷新Cookie时出错: {e}")

    def _refresh_expired_cookies(self):
        """刷新过期的Cookie"""
        with self.lock:
            expired_cookies = []
            for cookie_id, manager in self.cookie_managers.items():
                if manager.is_expired(self.max_failures) and not manager.in_use:
                    expired_cookies.append(cookie_id)

            if expired_cookies:
                logging.info(f"发现 {len(expired_cookies)} 个过期Cookie，开始刷新...")
                for cookie_id in expired_cookies:
                    manager = self.cookie_managers[cookie_id]
                    manager.delete_files()
                    threading.Thread(target=self._create_new_cookie, args=(cookie_id,), daemon=True).start()

    def get_pool_status(self):
        """获取池状态"""
        with self.lock:
            total = len(self.cookie_managers)
            available = len([m for m in self.cookie_managers.values() if not m.in_use and not m.is_expired(self.max_failures)])
            in_use = len([m for m in self.cookie_managers.values() if m.in_use])
            expired = len([m for m in self.cookie_managers.values() if m.is_expired(self.max_failures)])

            # 详细状态
            status_detail = {}
            for cookie_id, manager in self.cookie_managers.items():
                status_detail[cookie_id] = {
                    "failure_count": manager.failure_count,
                    "in_use": manager.in_use,
                    "expired": manager.is_expired(self.max_failures),
                    "last_used": manager.last_used.isoformat()
                }

            return {
                "total": total,
                "available": available,
                "in_use": in_use,
                "expired": expired,
                "detail": status_detail
            }


# 全局Cookie池实例
_cookie_pool = None

def get_cookie_pool():
    """获取全局Cookie池实例"""
    global _cookie_pool
    if _cookie_pool is None:
        _cookie_pool = CookiePool()
    return _cookie_pool


if __name__ == "__main__":
    asyncio.run(main())
