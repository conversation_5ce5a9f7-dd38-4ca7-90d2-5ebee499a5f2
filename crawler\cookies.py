# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON> Time: 2025/3/19 17:12
File Name:cookies.py
"""
import asyncio
import random
import logging
import json
import os
import threading
import time
from datetime import datetime, timedelta
from playwright.async_api import async_playwright
from crawler.proxy import ProxyManager
from crawler.config import get_cookie_config, get_proxy_config

if not os.path.exists('cookies'):
    os.makedirs('cookies')


class HumanBehaviorSimulator:
    """人类行为模拟工具类"""

    @staticmethod
    def get_random_viewport():
        """生成随机设备视口"""
        return {
            "width": random.choice([1920, 1366, 1536, 1440]),
            "height": random.choice([1080, 768, 864, 900])
        }

    @staticmethod
    async def simulate_typing(element, text):
        """模拟人类输入行为"""
        for char in text:
            await element.type(char)
            await asyncio.sleep(random.uniform(0.08, 0.15))
            if random.random() < 0.07:
                await element.press("Backspace")
                await element.type(char)

    @staticmethod
    async def random_mouse_movement(page):
        """生成随机鼠标轨迹"""
        for _ in range(random.randint(3, 5)):
            x = random.randint(0, page.viewport_size['width'])
            y = random.randint(0, page.viewport_size['height'])
            await page.mouse.move(x, y, steps=random.randint(2, 5))
            await asyncio.sleep(random.uniform(0.3, 0.7))

    @staticmethod
    async def simulate_scroll(page):
        """模拟自然滚动"""
        scroll_distance = random.randint(300, 800)
        await page.mouse.wheel(0, scroll_distance)
        await asyncio.sleep(random.uniform(0.5, 1.2))

    @staticmethod
    async def random_page_interaction(page):
        """随机页面交互"""
        actions = [
            lambda: page.mouse.wheel(0, random.randint(200, 500)),
            lambda: HumanBehaviorSimulator.random_mouse_movement(page),
            lambda: page.keyboard.press('PageDown'),
            lambda: asyncio.sleep(random.uniform(0.5, 1.5))
        ]
        for _ in range(random.randint(2, 4)):
            await random.choice(actions)()
            await asyncio.sleep(random.uniform(0.3, 0.8))


class GoogleCookie:
    """谷歌Cookie类，合并了管理和获取功能"""

    def __init__(self, cookie_file="google_cookies.json", ):
        self.cookies = {}
        self.cookie_file = cookie_file
        self.human_simulator = HumanBehaviorSimulator()
        self.config = {
            'timeout': 30000,
            'headless': True
        }
        self.load_cookies_from_file()

    def load_cookies_from_file(self):
        """从文件加载cookies"""
        if os.path.exists(self.cookie_file):
            try:
                with open(self.cookie_file, 'r') as f:
                    self.cookies = json.load(f)
                if self.cookies == {}:
                    self.create_cookies()
                logging.info("Cookies loaded from file")
            except:
                logging.warning("Failed to load cookies from file")
                self.cookies = {}

    def save_cookies_to_file(self):
        """保存cookies到文件"""
        with open(self.cookie_file, 'w') as f:
            json.dump(self.cookies, f)
        logging.info("Cookies saved to file")

    def get_cookies(self):
        """获取cookies"""
        return self.cookies

    def update_cookies(self, new_cookies):
        """更新cookies"""
        self.cookies = new_cookies
        self.save_cookies_to_file()

    def is_cookies_valid(self):
        """检查cookies是否有效"""
        if not self.cookies:
            return False

        # 简单检查cookie是否包含必要的字段
        required_cookies = ['AEC', 'NID']
        for cookie in required_cookies:
            if cookie not in self.cookies:
                return False

        return True

    async def _detect_captcha(self, page):
        """检测是否出现验证码"""
        captcha_selectors = [
            'form#captcha-form',
            'div.g-recaptcha',
            'input[name="captcha"]',
            'title:has-text("unusual traffic")'
        ]

        for selector in captcha_selectors:
            if await page.query_selector(selector):
                logging.warning("检测到验证码!")
                return True
        return False

    async def _perform_search(self, page, query):
        """执行搜索操作"""
        logging.info(f"正在执行搜索操作: {query}")
        await page.goto('https://www.google.com', timeout=self.config.get('timeout', 30000))
        await page.wait_for_load_state('networkidle')

        search_box = await page.wait_for_selector('textarea[name="q"]', timeout=15000)
        await HumanBehaviorSimulator.simulate_typing(search_box, query)
        await asyncio.sleep(random.uniform(0.8, 1.3))
        await search_box.press('Enter')

        await page.wait_for_selector('div#search', timeout=20000)
        return not await self._detect_captcha(page)

    async def fetch_new_cookies(self, search_query="python programming"):
        """获取谷歌搜索页面的cookies"""
        async with async_playwright() as p:
            # 检查代理配置
            proxy_config = get_proxy_config()
            if proxy_config["enabled"]:
                logging.info("代理已启用，获取代理用于Cookie创建")
                proxy_manager = ProxyManager(num=2, minute=5)
                proxy = proxy_manager.get_random_proxy()  # ip:port
                if proxy and ':' in proxy:
                    proxy_server = {'server': f'http://{proxy}'}
                    logging.info(f"使用代理创建Cookie: {proxy}")
                else:
                    proxy_server = None
                    logging.warning("代理已启用但无可用代理，直接连接创建Cookie")
            else:
                proxy_server = None
                logging.info("代理已禁用，直接连接创建Cookie")
            # 随机选择一个浏览器UA
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ]
            # 浏览器启动选项
            browser_args = [
                '--disable-blink-features=AutomationControlled',
                '--disable-features=IsolateOrigins,site-per-process',
                '--no-sandbox'
            ]

            # 浏览器配置
            browser_options = {
                'headless': self.config.get('headless', True),
                'args': browser_args,
                "ignore_default_args": ['--enable-automation']
            }
            logging.info(f"使用代理服务器: {proxy_server}  获取cookies")
            browser = await p.chromium.launch(**browser_options, proxy=proxy_server, timeout=60000)

            # 创建上下文
            viewport = self.human_simulator.get_random_viewport()
            context = await browser.new_context(
                viewport=viewport,
                user_agent=random.choice(user_agents)
            )

            # 禁用WebDriver
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });

                // 隐藏自动化特征
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({state: Notification.permission}) :
                        originalQuery(parameters)
                );

                // 模拟插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [installer.txt, 2, 3, 4, 5].map(() => ({
                        0: {
                            type: "application/x-google-chrome-pdf",
                            suffixes: "pdf",
                            description: "Portable Document Format"
                        },
                        name: "Chrome PDF Plugin",
                        filename: "internal-pdf-viewer",
                        description: "Portable Document Format",
                        length: installer.txt
                    }))
                });
            """)

            # 创建新页面
            page = await context.new_page()

            try:
                # 执行搜索操作
                search_success = await self._perform_search(page, search_query)

                if not search_success:
                    logging.warning("搜索操作失败，可能被检测为机器人")
                    return {}

                # 模拟随机互动
                await self.human_simulator.random_page_interaction(page)

                # 获取cookies
                cookies = await context.cookies()
                formatted_cookies = {}

                # 格式化cookies为字典
                for cookie in cookies:
                    if cookie.get('domain', '').endswith('google.com'):
                        formatted_cookies[cookie['name']] = cookie['value']

                # 更新cookie
                logging.info(f"获取新的cookies: {formatted_cookies}")
                self.update_cookies(formatted_cookies)
                logging.info("Cookies fetched successfully!")

                # 返回cookies
                return formatted_cookies

            except Exception as e:
                logging.error(f"Error fetching cookies: {e}")
                return {}

            finally:
                await browser.close()

    def create_cookies(self):
        """获取有效的cookies，如果无效则重新获取"""
        # 随机生成搜索词，使获取cookie的过程更自然
        search_queries = [
            "what is LLM",
            "news headlines",
            "best restaurants near me",
            "how to learn python",
            "upcoming movies",
            "stock market news",
            "healthy recipes",
            "travel destinations",
            "latest technology trends",
            "home workout routines",
            "best restaurants in town",
            "best coffee shops in the city",
            "popular music tracks",
            "best places to visit in Europe",
            "best restaurants in my city",
            "best restaurants in my country",
            "best restaurants in my stat",
            "chinese food",
            "japanese food",
            "italian food",
            "what is popmart",
            "how to learn python",
            "how to learn japanese",
            "how to learn chinese",
            "LLM",
            "agentic rag",
            "llm agent",
            "what is KG",
            "graphrag",
            "lightrag"
        ]

        # 获取新的cookies
        cookies = asyncio.run(self.fetch_new_cookies(random.choice(search_queries)))
        return cookies


async def main():
    cookie_handler = GoogleCookie()

    # 获取cookies，如果现有cookie无效则自动获取新cookie
    cookies = cookie_handler.get_cookies()
    if not cookie_handler.is_cookies_valid():
        logging.info("现有cookie无效，正在获取新cookie...")
        cookies = await cookie_handler.fetch_new_cookies()

    logging.info("当前cookie:", cookies)


class CookieInfo:
    """Cookie信息类"""
    def __init__(self, cookies, cookie_id):
        self.cookies = cookies
        self.cookie_id = cookie_id
        self.failure_count = 0
        self.last_used = datetime.now()
        self.in_use = False

    def mark_failure(self):
        """标记失败"""
        self.failure_count += 1
        self.last_used = datetime.now()

    def mark_success(self):
        """标记成功"""
        self.failure_count = 0
        self.last_used = datetime.now()

    def is_expired(self, max_failures):
        """检查是否已过期（失败次数过多）"""
        return self.failure_count >= max_failures


class CookiePool:
    """Cookie池管理类"""

    def __init__(self):
        self.config = get_cookie_config()
        self.pool_size = self.config["pool_size"]
        self.max_failures = self.config["max_failures"]
        self.refresh_interval = self.config["refresh_interval"]

        self.cookies_pool = []
        self.lock = threading.Lock()
        self.next_cookie_id = 0

        # 初始化Cookie池
        self._initialize_pool()

        # 启动后台刷新线程
        self.refresh_thread = threading.Thread(target=self._refresh_worker, daemon=True)
        self.refresh_thread.start()

        logging.info(f"Cookie池初始化完成，目标大小: {self.pool_size}")

    def _initialize_pool(self):
        """初始化Cookie池"""
        logging.info("正在初始化Cookie池...")
        for i in range(self.pool_size):
            try:
                cookie_handler = GoogleCookie(cookie_file=f"cookies/pool_cookie_{i}.json")
                cookies = cookie_handler.create_cookies()
                if cookies:
                    cookie_info = CookieInfo(cookies, self.next_cookie_id)
                    self.cookies_pool.append(cookie_info)
                    self.next_cookie_id += 1
                    logging.info(f"成功创建Cookie {i+1}/{self.pool_size}")
                else:
                    logging.warning(f"创建Cookie {i+1}失败")
            except Exception as e:
                logging.error(f"初始化Cookie {i+1}时出错: {e}")

        logging.info(f"Cookie池初始化完成，实际大小: {len(self.cookies_pool)}")

    def get_cookie(self):
        """获取一个可用的Cookie"""
        with self.lock:
            # 查找可用的Cookie（未在使用且未过期）
            available_cookies = [
                cookie_info for cookie_info in self.cookies_pool
                if not cookie_info.in_use and not cookie_info.is_expired(self.max_failures)
            ]

            if not available_cookies:
                logging.warning("没有可用的Cookie")
                return None

            # 选择最少使用的Cookie
            cookie_info = min(available_cookies, key=lambda x: x.last_used)
            cookie_info.in_use = True

            logging.debug(f"分配Cookie {cookie_info.cookie_id}，失败次数: {cookie_info.failure_count}")
            return cookie_info

    def release_cookie(self, cookie_info, success=True):
        """释放Cookie"""
        with self.lock:
            if cookie_info in self.cookies_pool:
                cookie_info.in_use = False
                if success:
                    cookie_info.mark_success()
                else:
                    cookie_info.mark_failure()

                logging.debug(f"释放Cookie {cookie_info.cookie_id}，成功: {success}，失败次数: {cookie_info.failure_count}")

                # 如果Cookie失败次数过多，从池中移除
                if cookie_info.is_expired(self.max_failures):
                    self.cookies_pool.remove(cookie_info)
                    logging.info(f"移除过期Cookie {cookie_info.cookie_id}")

                    # 异步补充新Cookie
                    threading.Thread(target=self._add_new_cookie, daemon=True).start()

    def _add_new_cookie(self):
        """添加新Cookie到池中"""
        try:
            cookie_handler = GoogleCookie(cookie_file=f"cookies/pool_cookie_{self.next_cookie_id}.json")
            cookies = cookie_handler.create_cookies()
            if cookies:
                with self.lock:
                    cookie_info = CookieInfo(cookies, self.next_cookie_id)
                    self.cookies_pool.append(cookie_info)
                    self.next_cookie_id += 1
                    logging.info(f"成功添加新Cookie {cookie_info.cookie_id}到池中")
        except Exception as e:
            logging.error(f"添加新Cookie时出错: {e}")

    def _refresh_worker(self):
        """后台刷新工作线程"""
        while True:
            try:
                time.sleep(self.refresh_interval)
                self._refresh_expired_cookies()
            except Exception as e:
                logging.error(f"刷新Cookie时出错: {e}")

    def _refresh_expired_cookies(self):
        """刷新过期的Cookie"""
        with self.lock:
            current_size = len(self.cookies_pool)
            if current_size < self.pool_size:
                needed = self.pool_size - current_size
                logging.info(f"Cookie池不足，需要补充 {needed} 个Cookie")

                for _ in range(needed):
                    threading.Thread(target=self._add_new_cookie, daemon=True).start()

    def get_pool_status(self):
        """获取池状态"""
        with self.lock:
            total = len(self.cookies_pool)
            available = len([c for c in self.cookies_pool if not c.in_use and not c.is_expired(self.max_failures)])
            in_use = len([c for c in self.cookies_pool if c.in_use])
            expired = len([c for c in self.cookies_pool if c.is_expired(self.max_failures)])

            return {
                "total": total,
                "available": available,
                "in_use": in_use,
                "expired": expired
            }


# 全局Cookie池实例
_cookie_pool = None

def get_cookie_pool():
    """获取全局Cookie池实例"""
    global _cookie_pool
    if _cookie_pool is None:
        _cookie_pool = CookiePool()
    return _cookie_pool


if __name__ == "__main__":
    asyncio.run(main())
