#!/bin/bash

while true; do
    /root/google-search/sh/cleanup_browsers.sh

    nohup python /root/google-search/main.py > /dev/null 2>&1 &
    MAIN_PID=$!
    echo "Main.py started with PID: $MAIN_PID"

    sleep 1800

    if kill -0 "$MAIN_PID" 2>/dev/null; then
        kill "$MAIN_PID"
        echo "Killed main.py (PID: $MAIN_PID)"
    else
        echo "Process $MAIN_PID already terminated"
    fi
    sleep 60
done