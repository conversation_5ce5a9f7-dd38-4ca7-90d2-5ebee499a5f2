# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON>i
<PERSON>reate Time: 2025/7/28
File Name: main.py
"""
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QSpinBox, QRadioButton,
                             QDateTimeEdit, QPushButton, QProgressBar,
                             QTextEdit, QGroupBox, QButtonGroup, QMessageBox, QDesktopWidget)
from PyQt5.QtCore import QDateTime, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import time
from datetime import datetime


class DeleteWorker(QThread):
    """删除数据的工作线程"""
    progress_updated = pyqtSignal(int, int, str)  # 已删除数量, 总数量, 剩余时间
    finished = pyqtSignal(str)  # 完成信号
    error = pyqtSignal(str)  # 错误信号
    
    def __init__(self, delete_type, id_threshold=None, datetime_threshold=None):
        super().__init__()
        self.delete_type = delete_type  # 'id' 或 'datetime'
        self.batch_size = 100000  # 固定批次大小为10000
        self.id_threshold = id_threshold
        self.datetime_threshold = datetime_threshold
        self.is_running = True
        
        # 数据库连接
        self.engine = None
        self.Session = None
        
    def setup_database(self):
        """设置数据库连接"""
        try:
            mysql_host = '************'
            mysql_port = 13306
            mysql_user ='root'
            mysql_password = 'qwer1234'
            mysql_database = 'ebay'
            
            connection_string = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_database}"
            self.engine = create_engine(connection_string)
            self.Session = sessionmaker(bind=self.engine)
            return True
        except Exception as e:
            self.error.emit(f"数据库连接失败: {str(e)}")
            return False
    
    def get_total_count(self):
        """获取要删除的总记录数"""
        try:
            session = self.Session()
            if self.delete_type == 'id':
                query = text(f"SELECT COUNT(*) FROM search_results WHERE id <= {self.id_threshold}")
            else:  # datetime
                query = text(f"SELECT COUNT(*) FROM search_results WHERE date <= '{self.datetime_threshold}'")
            
            result = session.execute(query)
            count = result.scalar()
            session.close()
            return count
        except Exception as e:
            self.error.emit(f"获取记录数失败: {str(e)}")
            return 0
    
    def run(self):
        """执行删除操作"""
        if not self.setup_database():
            return
            
        total_count = self.get_total_count()
        if total_count == 0:
            self.finished.emit("没有找到符合条件的记录")
            return
            
        deleted_count = 0
        start_time = time.time()
        
        try:
            while self.is_running and deleted_count < total_count:
                session = self.Session()
                
                # 构建删除查询
                if self.delete_type == 'id':
                    delete_query = text(f"""
                        DELETE FROM search_results 
                        WHERE id <= {self.id_threshold} 
                        LIMIT {self.batch_size}
                    """)
                else:  # datetime
                    delete_query = text(f"""
                        DELETE FROM search_results 
                        WHERE date <= '{self.datetime_threshold}' 
                        LIMIT {self.batch_size}
                    """)
                
                # 执行删除
                result = session.execute(delete_query)
                session.commit()
                batch_deleted = result.rowcount
                session.close()
                
                if batch_deleted == 0:
                    break
                    
                deleted_count += batch_deleted
                
                # 计算剩余时间
                elapsed_time = time.time() - start_time
                if deleted_count > 0:
                    avg_time_per_record = elapsed_time / deleted_count
                    remaining_records = total_count - deleted_count
                    remaining_time = avg_time_per_record * remaining_records
                    remaining_time_str = self.format_time(remaining_time)
                else:
                    remaining_time_str = "计算中..."
                
                # 发送进度更新信号
                self.progress_updated.emit(deleted_count, total_count, remaining_time_str)
                
                # 短暂休息，避免数据库压力过大
                time.sleep(0.1)
                
        except Exception as e:
            self.error.emit(f"删除过程中出错: {str(e)}")
            return
            
        if self.is_running:
            self.finished.emit(f"删除完成！共删除 {deleted_count} 条记录")
        else:
            self.finished.emit(f"删除已停止！已删除 {deleted_count} 条记录")
    
    def format_time(self, seconds):
        """格式化时间显示"""
        if seconds < 60:
            return f"{int(seconds)}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}分{secs}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}小时{minutes}分钟"
    
    def stop(self):
        """停止删除操作"""
        self.is_running = False


class DeleteMainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.worker = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("数据库记录删除工具")
        self.resize(800, 600)

        # 将窗口居中显示
        self.center_window()
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 删除条件组
        condition_group = QGroupBox("删除条件")
        condition_layout = QVBoxLayout(condition_group)
        
        # 删除方式选择
        self.delete_type_group = QButtonGroup()

        # 按ID删除
        self.id_radio = QRadioButton("按ID删除")
        self.id_radio.setChecked(True)  # 默认选择按ID删除
        self.delete_type_group.addButton(self.id_radio)
        condition_layout.addWidget(self.id_radio)

        # ID选择
        id_layout = QHBoxLayout()
        id_layout.addWidget(QLabel("删除ID小于等于:"))
        self.id_spinbox = QSpinBox()
        self.id_spinbox.setRange(1, 999999999)
        self.id_spinbox.setValue(1000)
        id_layout.addWidget(self.id_spinbox)
        id_layout.addStretch()
        condition_layout.addLayout(id_layout)

        # 按时间删除
        self.datetime_radio = QRadioButton("按时间删除")
        self.delete_type_group.addButton(self.datetime_radio)
        condition_layout.addWidget(self.datetime_radio)

        # 时间选择
        datetime_layout = QHBoxLayout()
        datetime_layout.addWidget(QLabel("删除时间早于:"))
        self.datetime_edit = QDateTimeEdit()
        self.datetime_edit.setDateTime(QDateTime.currentDateTime())
        self.datetime_edit.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.datetime_edit.setCalendarPopup(True)  # 启用日历弹出
        datetime_layout.addWidget(self.datetime_edit)
        datetime_layout.addStretch()
        condition_layout.addLayout(datetime_layout)
        
        main_layout.addWidget(condition_group)
        
        # 进度显示组
        progress_group = QGroupBox("删除进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        # 进度信息
        self.progress_label = QLabel("进度: 0/0 (0%)")
        progress_layout.addWidget(self.progress_label)
        
        # 剩余时间
        self.time_label = QLabel("预计剩余时间: --")
        progress_layout.addWidget(self.time_label)
        
        main_layout.addWidget(progress_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始删除")
        self.start_button.clicked.connect(self.start_delete)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_delete)
        button_layout.addWidget(self.stop_button)
        
        main_layout.addLayout(button_layout)
        
        # 日志显示
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)
        
        main_layout.addWidget(log_group)

    def center_window(self):
        """将窗口居中显示"""
        screen = QDesktopWidget().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def start_delete(self):
        """开始删除操作"""
        try:
            # 获取参数
            if self.id_radio.isChecked():
                delete_type = 'id'
                id_threshold = self.id_spinbox.value()
                datetime_threshold = None
            else:
                delete_type = 'datetime'
                datetime_threshold = self.datetime_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")
                id_threshold = None

            # 创建工作线程
            self.worker = DeleteWorker(delete_type, id_threshold, datetime_threshold)
            self.worker.progress_updated.connect(self.update_progress)
            self.worker.finished.connect(self.delete_finished)
            self.worker.error.connect(self.delete_error)

            # 更新UI状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.progress_bar.setValue(0)

            # 添加日志
            self.add_log(f"开始删除操作 - 批次大小: 10000")
            if delete_type == 'id':
                self.add_log(f"删除条件: ID小于等于 {id_threshold}")
            else:
                self.add_log(f"删除条件: 时间早于 {datetime_threshold}")

            # 启动线程
            self.worker.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动删除操作失败: {str(e)}")
    
    def stop_delete(self):
        """停止删除操作"""
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.add_log("正在停止删除操作...")
    
    def update_progress(self, deleted_count, total_count, remaining_time):
        """更新进度显示"""
        if total_count > 0:
            progress = int((deleted_count / total_count) * 100)
            self.progress_bar.setValue(progress)
            self.progress_label.setText(f"进度: {deleted_count}/{total_count} ({progress}%)")
        else:
            self.progress_label.setText(f"进度: {deleted_count}/0")
            
        self.time_label.setText(f"预计剩余时间: {remaining_time}")
    
    def delete_finished(self, message):
        """删除完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.add_log(message)
        QMessageBox.information(self, "完成", message)
    
    def delete_error(self, error_message):
        """删除出错"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.add_log(f"错误: {error_message}")
        QMessageBox.critical(self, "错误", error_message)
    
    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")


def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    window = DeleteMainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
