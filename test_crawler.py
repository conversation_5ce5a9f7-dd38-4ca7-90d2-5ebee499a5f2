# -*- coding: utf-8 -*-
"""
测试爬虫新架构
"""

import sys
import os

# 添加crawler目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'crawler'))

def test_imports():
    """测试所有模块是否能正常导入"""
    try:
        print("测试配置模块...")
        from crawler.config import get_config, get_cookie_config
        config = get_config()
        print(f"✓ 配置加载成功: 线程数={config['threads']}, Cookie池大小={config['cookie_pool']['pool_size']}")
        
        print("\n测试代理模块...")
        from crawler.proxy import ProxyManager
        print("✓ 代理模块导入成功")
        
        print("\n测试Cookie模块...")
        from crawler.cookies import GoogleCookie, CookiePool, get_cookie_pool
        print("✓ Cookie模块导入成功")
        
        print("\n测试爬虫模块...")
        from crawler.spider import GoogleSearch, GoogleSearchThreadManager
        print("✓ 爬虫模块导入成功")
        
        print("\n测试主模块...")
        from crawler.main import main
        print("✓ 主模块导入成功")
        
        print("\n🎉 所有模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """测试配置功能"""
    try:
        from crawler.config import get_config, get_cookie_config, get_proxy_config
        
        config = get_config()
        cookie_config = get_cookie_config()
        proxy_config = get_proxy_config()
        
        print("\n=== 配置信息 ===")
        print(f"线程数: {config['threads']}")
        print(f"批次大小: {config['batch_size']}")
        print(f"Cookie池大小: {cookie_config['pool_size']}")
        print(f"Cookie最大失败次数: {cookie_config['max_failures']}")
        print(f"查询最大重试次数: {cookie_config['max_retries']}")
        print(f"代理启用: {proxy_config['enabled']}")
        print(f"代理池大小: {proxy_config['proxy_pool_size']}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_cookie_pool():
    """测试Cookie池（不实际创建Cookie）"""
    try:
        print("\n=== Cookie池测试 ===")
        from crawler.cookies import CookieInfo
        from datetime import datetime
        
        # 创建测试Cookie信息
        test_cookies = {"test": "cookie"}
        cookie_info = CookieInfo(test_cookies, 1)
        
        print(f"Cookie ID: {cookie_info.cookie_id}")
        print(f"失败次数: {cookie_info.failure_count}")
        print(f"是否在使用: {cookie_info.in_use}")
        
        # 测试失败标记
        cookie_info.mark_failure()
        print(f"标记失败后失败次数: {cookie_info.failure_count}")
        
        # 测试成功标记
        cookie_info.mark_success()
        print(f"标记成功后失败次数: {cookie_info.failure_count}")
        
        print("✓ Cookie信息类测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Cookie池测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试新的爬虫架构...")
    
    success = True
    success &= test_imports()
    success &= test_config()
    success &= test_cookie_pool()
    
    if success:
        print("\n🎉 所有测试通过！新架构已准备就绪。")
        print("\n使用方法:")
        print("1. 直接运行: python crawler/main.py")
        print("2. 使用启动脚本: python run_crawler.py")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
