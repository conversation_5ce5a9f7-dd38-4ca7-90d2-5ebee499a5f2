# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/7/31
File Name: config.py
配置文件
"""

# 爬虫配置
CRAWLER_CONFIG = {
    # 线程配置
    "threads": 4,
    "batch_size": 40,
    
    # Cookie池配置
    "cookie_pool": {
        "pool_size": 10,        # <PERSON>ie池大小
        "max_failures": 3,      # Cookie最大失败次数后删除
        "max_retries": 5,       # 查询最大重试次数（使用不同Cookie）
        "refresh_interval": 3600,  # Cookie刷新间隔（秒）
    },
    
    # 代理配置
    "proxy": {
        "enabled": False,
        "max_errors_before_change": 3,  # 更换代理前允许的最大错误次数
        "proxy_pool_size": 20,         # 代理池大小
    },
    
    # 搜索配置
    "search": {
        "max_pages": None,              # 最大爬取页数，None表示爬取所有页面
        "page_delay": (0.5, 1.5),      # 页面间延迟范围（秒）
        "request_timeout": 10,          # 请求超时时间（秒）
    },
    
    # 日志配置
    "logging": {
        "level": "INFO",
        "file": "google_search.log",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    }
}

# 获取配置函数
def get_config():
    """获取配置"""
    return CRAWLER_CONFIG

def get_cookie_config():
    """获取Cookie配置"""
    return CRAWLER_CONFIG["cookie_pool"]

def get_proxy_config():
    """获取代理配置"""
    return CRAWLER_CONFIG["proxy"]

def get_search_config():
    """获取搜索配置"""
    return CRAWLER_CONFIG["search"]

def get_logging_config():
    """获取日志配置"""
    return CRAWLER_CONFIG["logging"]
