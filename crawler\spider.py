# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/3/19 13:15
File Name: spider.py
"""
import random
import time
from datetime import datetime
import logging

import requests
import threading
import re
from lxml import etree
import os

from sqlalchemy import func

from crawler.cookies import Google<PERSON><PERSON>ie, get_cookie_pool
from crawler.proxy import ProxyManager
from crawler.config import get_config, get_search_config, get_proxy_config
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db.db_models import SearchQueries, SearchResults
from db.init_db import SessionLocal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('google_search.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('GoogleSearch')


class GoogleSearch:
    def __init__(self, thread_id=0, proxy_manager=None):
        """
        Initialize the GoogleSearch class

        Args:
            thread_id: 线程ID,用于区分不同实例
            proxy_manager: 代理管理器实例 (默认: 创建新的MyProxyManager)
        """
        self.thread_id = thread_id
        self.cookie_pool = get_cookie_pool()
        self.proxy_manager = proxy_manager
        self.config = get_search_config()
        self.cookie_config = get_config()["cookie_pool"]
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
        ]
        self.base_headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'downlink': '10',
            'priority': 'u=0, i',
            'rtt': '50',
            'sec-ch-prefers-color-scheme': 'dark',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-arch': '"x86"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-form-factors': '"Desktop"',
            'sec-ch-ua-full-version': '"133.0.6943.142"',
            'sec-ch-ua-full-version-list': '"Not(A:Brand";v="99.0.0.0", "Google Chrome";v="133.0.6943.142", "Chromium";v="133.0.6943.142"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua-platform-version': '"15.0.0"',
            'sec-ch-ua-wow64': '?0',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?installer.txt',
            'upgrade-insecure-requests': 'installer.txt',
            'x-browser-channel': 'stable',
            'x-browser-copyright': 'Copyright 2025 Google LLC. All rights reserved.',
            'x-browser-year': '2025',
        }

    def get_headers(self):
        """生成带有随机User-Agent的请求头"""
        headers = self.base_headers.copy()
        headers['user-agent'] = random.choice(self.user_agents)
        return headers

    def check_cookies_valid(self, page):
        h3_elements = page.xpath('//h3')
        return len(h3_elements) >= 1

    def get_email(self, content):
        """从内容中提取邮箱地址，增强版"""
        if not content:
            return None

        # 基本邮箱正则表达式 - 更严格的域名匹配
        basic_pattern = r'[\w\.-]+@[\w\.-]+\.(?:com|net|org|edu|gov|mil|io|co|uk|cn|de|fr|jp|au|ru|ca|it|nl|es|br|in|mx|ch|se|no|dk|fi|pl|cz|at|be|ie|nz|sg|hu|pt|gr|il|za|tr|ro|hk|kr|tw|vn|id|th|my|ph|sa|ae|qa|pk|bd|ng|ke|tz|gh|et|dz|ma|tn|eg|ly|zw|zm|mw|mu|mg|ci|cm|sn|cd|ao|na|bw|ls|sz|rw|bi|tg|bj|ne|ml|mr|td|gm|sl|lr|gn|gw|cv|st|ga|cg|cf|sd|ss|er|dj|so)'

        # 尝试提取标准邮箱
        emails = re.findall(basic_pattern, content, re.IGNORECASE)
        if emails:
            return emails[0].strip()

        # 处理带空格的邮箱
        spaced_email_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*(com|net|org|edu|gov|mil|io|co|uk|cn|de|fr|jp|au|ru|ca|it|nl|es|br|in|mx)'
        spaced_match = re.search(spaced_email_pattern, content, re.IGNORECASE)
        if spaced_match:
            return f"{spaced_match.group(1)}@{spaced_match.group(2)}.{spaced_match.group(3)}"

        # 针对特殊情况：邮箱后面紧跟着句点和文本
        # 例如: "hughnick1@ outlook.com. Village Website"
        special_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*(com|net|org|edu|gov|mil|io|co|uk)(?:\s*\.\s*|\s+)'
        special_match = re.search(special_pattern, content, re.IGNORECASE)
        if special_match:
            return f"{special_match.group(1)}@{special_match.group(2)}.{special_match.group(3)}"

        # 尝试查找并组合分散的邮箱部分
        if '@' in content:
            # 使用更复杂的正则表达式来处理分散的邮箱
            combined_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*([a-zA-Z]{2,6})'
            combined_match = re.search(combined_pattern, content)
            if combined_match:
                username = combined_match.group(1).strip()
                domain = combined_match.group(2).strip()
                tld = combined_match.group(3).strip()
                return f"{username}@{domain}.{tld}"

        return None

    def search_page(self, keyword, page_num=0, proxy=None, error_count=0):
        """
        搜索特定页码的结果并提取URL和邮箱，使用Cookie池重试机制

        Args:
            keyword: 搜索关键词
            page_num: 页码 (0表示第一页)
            proxy: 使用的代理
            error_count: 当前错误计数

        Returns:
            结果字典，包含results和has_next，以及更新后的proxy和error_count
        """
        # 搜索参数
        params = {
            'q': keyword,
            'oq': keyword,
            'start': page_num * 10,  # Google每页10个结果
            'gs_lcrp': '',
            'sourceid': 'chrome',
            'ie': 'UTF-8',
        }

        # 设置代理
        if proxy:
            proxy_server = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}"
            }
        else:
            proxy_server = None

        # 使用Cookie池重试机制
        max_retries = self.cookie_config["max_retries"]

        for attempt in range(max_retries):
            # 从Cookie池获取Cookie
            cookie_info = self.cookie_pool.get_cookie()
            if not cookie_info:
                logger.error(f"[线程 {self.thread_id}] 无法获取可用Cookie，尝试 {attempt + 1}/{max_retries}")
                time.sleep(1)  # 等待一秒后重试
                continue

            try:
                headers = self.get_headers()
                res = requests.get(
                    url='https://www.google.com/search',
                    headers=headers,
                    cookies=cookie_info.cookies,
                    params=params,
                    timeout=self.config.get("request_timeout", 10),
                    proxies=proxy_server
                )

                if res.status_code == 200:
                    page = etree.HTML(res.text)

                    # 检查是否有有效结果
                    if not self.check_cookies_valid(page):
                        logger.info(f"[线程 {self.thread_id}] Cookie {cookie_info.cookie_id} 无效，尝试下一个")
                        self.cookie_pool.release_cookie(cookie_info, success=False)
                        error_count += 1
                        continue

                    # 提取搜索结果
                    url_elements = page.xpath('//a[@jsname="UWckNb"]/@href')

                    # 获取URL和内容列表
                    urls = [url for url in url_elements]
                    contents = [
                        ' '.join(page.xpath(f'(//div[@jscontroller="SC7lYd"]//*[@data-snf="nke7rc"])[{i + 1}]//text()'))
                        for i in range(len(url_elements))]

                    # 确保内容列表长度与URL列表相同
                    while len(contents) < len(urls):
                        contents.append('')  # 如果内容少于URL，添加空内容

                    # 提取结果
                    results = []
                    for i, url in enumerate(urls):
                        try:
                            content = contents[i] if i < len(contents) else ''

                            # 提取邮箱
                            email = self.get_email(content)
                            if email:
                                results.append((url, email))
                        except Exception as e:
                            logger.error(f"[线程 {self.thread_id}] 提取结果 {i + 1} 时出错: {e}")

                    # 检查是否有下一页
                    has_next = page.xpath('//a[@id="pnnext"]')

                    # 成功获取结果，释放Cookie并标记成功
                    self.cookie_pool.release_cookie(cookie_info, success=True)
                    logger.debug(f"[线程 {self.thread_id}] Cookie {cookie_info.cookie_id} 使用成功")

                    return {
                        'results': results,
                        'has_next': len(has_next) > 0,
                        'proxy': proxy,
                        'error_count': 0  # 成功后重置错误计数
                    }

                elif res.status_code == 429:
                    logger.warning(f"[线程 {self.thread_id}] Cookie {cookie_info.cookie_id} 被限制 (429)，标记失败")
                    self.cookie_pool.release_cookie(cookie_info, success=False)
                    error_count += 1

                else:
                    logger.warning(f"[线程 {self.thread_id}] Cookie {cookie_info.cookie_id} 请求返回状态码: {res.status_code}")
                    self.cookie_pool.release_cookie(cookie_info, success=False)
                    error_count += 1

            except Exception as e:
                logger.error(f'[线程 {self.thread_id}] Cookie {cookie_info.cookie_id} 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次')
                self.cookie_pool.release_cookie(cookie_info, success=False)
                error_count += 1

            # 等待一段时间后重试
            time.sleep(random.uniform(0.5, 1.0))

        logger.error(f"[线程 {self.thread_id}] 所有 {max_retries} 次Cookie重试均失败")

        # 返回失败结果和更新的错误计数
        return {
            'results': [],
            'has_next': False,
            'proxy': proxy,
            'error_count': error_count
        }

    def search(self, keyword, max_pages=None, max_errors_before_proxy_change=None):
        """
        搜索并获取所有页面的结果

        Args:
            keyword: 搜索关键词
            max_pages: 最大爬取页数，None表示爬取所有页面
            max_errors_before_proxy_change: 更换代理前允许的最大错误次数

        Returns:
            所有页面的结果列表，每个元素是(page_num, url, email)元组
        """
        # 从配置获取参数
        if max_pages is None:
            max_pages = self.config.get("max_pages")
        if max_errors_before_proxy_change is None:
            max_errors_before_proxy_change = get_proxy_config()["max_errors_before_change"]

        all_results = []
        page_num = 0
        has_next = True
        error_count = 0

        # 初始获取一个代理
        if self.proxy_manager:
            proxy = self.proxy_manager.get_random_proxy()
            if proxy:
                logger.info(f"[线程 {self.thread_id}] 初始使用代理: {proxy}")
            else:
                logger.warning(f"[线程 {self.thread_id}] 代理已启用但没有可用代理")
        else:
            proxy = None
            logger.info(f"[线程 {self.thread_id}] 代理已禁用，直接连接")

        while has_next and (max_pages is None or page_num < max_pages):
            # 使用当前代理搜索页面
            page_result = self.search_page(keyword, page_num, proxy, error_count)

            # 更新代理和错误计数
            proxy = page_result['proxy']
            error_count = page_result['error_count']

            # 如果错误计数超过阈值，更换代理
            if error_count >= max_errors_before_proxy_change and self.proxy_manager:
                logger.warning(f"[线程 {self.thread_id}] 连续遇到 {error_count} 次错误，更换代理")

                # 获取新代理
                proxy = self.proxy_manager.get_random_proxy()
                if proxy:
                    logger.info(f"[线程 {self.thread_id}] 更换为新代理: {proxy}")
                else:
                    logger.warning(f"[线程 {self.thread_id}] 没有可用的新代理")

                error_count = 0  # 重置错误计数

            if not page_result['results']:
                logger.error(f"[线程 {self.thread_id}] 获取第 {page_num + 1} 页失败，停止爬取")
                break

            # 添加当前页面的结果
            for url, email in page_result['results']:
                all_results.append((page_num + 1, url, email))

            has_next = page_result['has_next']
            # logger.info(f"[线程 {self.thread_id}] 已爬取第 {page_num + installer.txt} 页，共找到 {len(page_result['results'])} 个结果")

            page_num += 1

            # 页面间添加随机延迟
            # if has_next and (max_pages is None or page_num < max_pages):
            #     time.sleep(random.uniform(installer.txt, 3))

        logger.info(f"[线程 {self.thread_id}] 完成搜索 '{keyword}'，共爬取 {page_num} 页，找到 {len(all_results)} 个结果")

        return all_results


class GoogleSearchThreadManager:
    def __init__(self, num_threads=None, batch_size=None, browser_path=None):
        """
        初始化多线程搜索管理器

        Args:
            num_threads: 线程数 (None时从配置读取)
            batch_size: 每批处理的数据量 (None时从配置读取)
            browser_path: 浏览器路径
        """
        config = get_config()
        self.num_threads = num_threads or config["threads"]
        self.batch_size = batch_size or config["batch_size"]
        self.result_lock = threading.Lock()
        self.threads = []
        self.thread_queries = []  # 每个线程负责的查询列表
        self.browser_path = browser_path

        # 进度跟踪
        self.total_queries = 0
        self.completed_queries = 0
        self.progress_callback = None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def update_progress(self, thread_id, completed):
        """更新并报告当前进度"""
        with self.result_lock:
            self.completed_queries += completed
            if self.progress_callback and self.total_queries > 0:
                progress = (self.completed_queries / self.total_queries) * 100
                self.progress_callback(self.completed_queries, self.total_queries, progress)
            logger.info(
                f"[线程 {thread_id}] 已完成 {completed} 个查询，总进度: {self.completed_queries}/{self.total_queries}")

    def get_uncrawled_queries(self):
        """从数据库获取未爬取的查询，随机选择而非按顺序"""
        try:
            db = SessionLocal()
            try:
                # 获取所有未爬取的查询，使用随机排序
                queries = db.query(SearchQueries).filter(
                    (SearchQueries.crawled_status.is_(None)) |
                    (SearchQueries.crawled_status != 'completed')
                ).order_by(func.random()).limit(self.batch_size).all()
                data =[]
                for query in queries:
                    if query:
                        data.append((query.id, query.county, query.goods, query.email_type))
                return data
            finally:
                db.close()
        except Exception as e:
            logger.error(f"获取未爬取查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def distribute_queries(self):
        """将查询分配给各个线程"""
        try:
            # 获取所有未处理的查询
            unprocessed_queries = self.get_uncrawled_queries()

            self.total_queries = len(unprocessed_queries)
            logger.info(f"共有 {self.total_queries} 个查询需要处理")

            if self.total_queries == 0:
                return []

            # 将查询平均分配给各个线程
            self.thread_queries = [[] for _ in range(self.num_threads)]
            for i, query_data in enumerate(unprocessed_queries):
                thread_idx = i % self.num_threads
                self.thread_queries[thread_idx].append(query_data)

            # 打印每个线程分配的查询数量
            for i, queries in enumerate(self.thread_queries):
                logger.info(f"线程 {i} 分配了 {len(queries)} 个查询")

            return self.thread_queries
        except Exception as e:
            logger.error(f"分配查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def worker(self, thread_id, queries):
        """
        工作线程函数

        Args:
            thread_id: 线程ID
            queries: 该线程负责处理的查询列表，每个元素是(id, county, goods, email_type)元组
        """
        if not queries:
            logger.info(f"[线程 {thread_id}] 没有分配到查询任务")
            return

        # 为每个线程创建一个GoogleSearch实例
        proxy_config = get_proxy_config()
        if proxy_config["enabled"]:
            logger.info(f"[线程 {thread_id}] 代理已启用，创建代理管理器（池大小: {proxy_config['proxy_pool_size']}）")
            proxy_manager = ProxyManager(num=proxy_config["proxy_pool_size"])
        else:
            logger.info(f"[线程 {thread_id}] 代理已禁用，将直接连接")
            proxy_manager = None
        searcher = GoogleSearch(thread_id=thread_id, proxy_manager=proxy_manager)

        completed = 0
        local_results = {}  # 本地存储结果，减少数据库访问 {id: (search_results, success)}

        for query_id, county, goods, email_type in queries:
            try:
                # 构建查询字符串: county + goods + email_type
                if email_type == '@email':
                    search_query = f'{county} {goods} {email_type}'
                else:
                    search_query = f'{county} {goods} "{email_type}"'

                # 执行搜索，获取所有页面的结果
                search_results = searcher.search(search_query)

                # 存储结果到本地字典，标记为成功
                local_results[query_id] = (search_results, True)

            except Exception as e:
                logger.error(f"[线程 {thread_id}] 处理查询 {county} {goods} {email_type} 时出错: {e}")
                # 存储空结果，标记为失败
                local_results[query_id] = ([], False)

            completed += 1

            # 每处理5个查询更新一次数据库
            if completed % 1 == 0 or completed == len(queries):
                self.update_results_db(local_results, thread_id, county, goods, email_type)
                local_results = {}  # 清空本地结果

            # 添加随机延迟，避免请求过于频繁
            # time.sleep(random.uniform(0.5, installer.txt.5))

        # 处理剩余的结果
        if local_results:
            self.update_results_db(local_results, thread_id, county, goods, email_type)
            self.update_progress(thread_id, len(local_results))

        logger.info(f"[线程 {thread_id}] 完成所有 {len(queries)} 个查询")

    def update_results_db(self, results_dict, thread_id, city, goods, email_type):
        """
        更新数据库中的查询结果

        Args:
            results_dict: 查询结果字典 {query_id: (search_results, success)}
            thread_id: 线程ID
        """
        if not results_dict:
            return

        try:
            # 使用线程锁保护数据库操作
            with self.result_lock:
                db = SessionLocal()
                try:
                    # 更新结果
                    for query_id, (search_results, success) in results_dict.items():
                        # 更新查询状态，无论成功失败都标记为completed
                        query = db.query(SearchQueries).filter(SearchQueries.id == query_id).first()
                        if query:
                            query.crawled_status = 'completed'

                            # 如果搜索成功且有结果，保存结果到数据库
                            if success and search_results:
                                for page_num, url, email in search_results:
                                    if email:
                                        result = SearchResults(
                                            url=url,
                                            email=email,
                                            current_page=page_num,
                                            city=city,
                                            goods=goods,
                                            email_type=email_type,
                                            date=datetime.now(),
                                        )
                                        db.add(result)

                    db.commit()

                except Exception as e:
                    db.rollback()
                    logger.error(f"[线程 {thread_id}] 更新数据库失败: {e}")
                    import traceback
                    traceback.print_exc()
                finally:
                    db.close()

            logger.info(f"[线程 {thread_id}] 已更新 {len(results_dict)} 个查询结果到数据库")
        except Exception as e:
            logger.error(f"[线程 {thread_id}] 更新结果数据库失败: {e}")
            import traceback
            traceback.print_exc()

    def start(self):
        """启动所有工作线程"""
        # 分配查询任务给各个线程
        thread_queries = self.distribute_queries()

        if not any(thread_queries):
            logger.info("没有需要处理的新查询")
            return False

        # 创建并启动工作线程
        for i in range(self.num_threads):
            if i < len(thread_queries) and thread_queries[i]:
                thread = threading.Thread(target=self.worker, args=(i, thread_queries[i]))
                thread.daemon = True  # 设置为守护线程
                self.threads.append(thread)
                thread.start()
                logger.info(f"启动线程 {i}")

        return True

    def wait_completion(self):
        """等待所有线程完成"""
        for thread in self.threads:
            thread.join()
        logger.info("所有线程已完成!")
