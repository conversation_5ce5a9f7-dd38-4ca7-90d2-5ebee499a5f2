# -*- coding: utf-8 -*-
"""
测试代理配置
"""

def test_proxy_config():
    """测试代理配置"""
    from crawler.config import get_proxy_config
    
    proxy_config = get_proxy_config()
    print(f"代理配置: {proxy_config}")
    print(f"代理启用: {proxy_config['enabled']}")
    
    if proxy_config['enabled']:
        print("⚠️ 代理已启用，将尝试获取代理")
    else:
        print("✅ 代理已禁用，不会获取代理")

def test_cookie_creation():
    """测试Cookie创建是否遵循代理配置"""
    from crawler.cookies import GoogleCookie
    import asyncio
    
    print("\n测试Cookie创建...")
    cookie_handler = GoogleCookie(cookie_file="test_cookie.json")
    
    # 这里不实际创建Cookie，只是测试配置读取
    from crawler.config import get_proxy_config
    proxy_config = get_proxy_config()
    
    if proxy_config['enabled']:
        print("Cookie创建将使用代理")
    else:
        print("Cookie创建将直接连接（不使用代理）")

if __name__ == "__main__":
    print("=== 测试代理配置 ===")
    test_proxy_config()
    test_cookie_creation()
    print("\n如果看到'代理已禁用'，说明配置正确！")
