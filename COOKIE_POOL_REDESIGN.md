# Cookie池重新设计说明

## 新的Cookie池架构

### 核心特性

1. **固定数量的Cookie文件**
   - 配置文件中设置 `pool_size = 10`，就会有10个固定的Cookie文件
   - 文件命名：`cookies/pool_cookie_0.json` 到 `cookies/pool_cookie_9.json`

2. **失败次数文件记录**
   - 每个Cookie都有对应的元数据文件：`cookies/pool_cookie_0_meta.json`
   - 记录失败次数、创建时间、最后使用时间等信息

3. **文件级别管理**
   - 失败次数达到阈值时，删除Cookie文件和元数据文件
   - 重新创建新的Cookie文件

4. **多线程安全**
   - 所有文件操作都有锁保护
   - 确保多线程环境下的数据一致性

## 文件结构

```
cookies/
├── pool_cookie_0.json      # Cookie内容
├── pool_cookie_0_meta.json # 元数据（失败次数等）
├── pool_cookie_1.json
├── pool_cookie_1_meta.json
├── ...
├── pool_cookie_9.json
└── pool_cookie_9_meta.json
```

## 元数据文件格式

```json
{
  "failure_count": 2,
  "created_time": "2025-07-31T16:30:00",
  "last_used": "2025-07-31T16:35:00"
}
```

## 工作流程

### 1. 初始化
- 创建固定数量的CookieFileManager
- 检查Cookie文件是否存在，不存在则创建
- 加载现有的失败次数记录

### 2. 获取Cookie
- 查找可用的Cookie（未在使用且未过期）
- 标记为使用中
- 返回CookieFileManager和Cookie内容

### 3. 释放Cookie
- 根据成功/失败更新失败次数
- 保存元数据到文件
- 如果失败次数达到阈值，删除文件并重新创建

### 4. 后台刷新
- 定期检查过期的Cookie
- 自动删除并重新创建

## 配置说明

```python
"cookie_pool": {
    "pool_size": 10,        # 固定的Cookie文件数量
    "max_failures": 3,      # 失败多少次后删除Cookie文件
    "max_retries": 5,       # 查询最大重试次数
    "refresh_interval": 3600,  # 后台刷新间隔
}
```

## 使用示例

```python
# 获取Cookie
cookie_result = cookie_pool.get_cookie()
if cookie_result:
    cookie_manager, cookies = cookie_result
    
    # 使用Cookie进行请求
    try:
        # ... 请求逻辑 ...
        # 成功
        cookie_pool.release_cookie(cookie_manager, success=True)
    except:
        # 失败
        cookie_pool.release_cookie(cookie_manager, success=False)
```

## 优势

1. **持久化失败记录**：失败次数保存在文件中，重启程序不会丢失
2. **固定数量管理**：始终维持配置的Cookie数量
3. **自动恢复**：失败的Cookie会被自动删除和重新创建
4. **线程安全**：多线程环境下安全使用
5. **状态透明**：可以查看每个Cookie的详细状态

## 测试方法

```bash
# 测试Cookie池管理
python test_cookie_pool.py

# 启动爬虫
python start.py
```

## 监控

可以通过 `get_pool_status()` 方法监控Cookie池状态：
- 总数、可用数、使用中数量、过期数量
- 每个Cookie的详细失败次数和状态
