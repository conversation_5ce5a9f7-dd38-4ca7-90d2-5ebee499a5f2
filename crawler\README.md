# Google Search Crawler

## 项目结构

```
crawler/
├── __init__.py          # 包初始化文件
├── config.py            # 配置文件
├── cookies.py           # Cookie池管理
├── proxy.py             # 代理管理
├── spider.py            # 爬虫核心逻辑
├── main.py              # 主函数
└── README.md            # 说明文档
```

## 新功能特性

### 1. Cookie池管理
- 维护n个Cookie的池子（可配置）
- Cookie失败m次后才删除（可配置）
- 自动补充Cookie池到目标数量
- 线程安全的Cookie获取和释放

### 2. 查询重试机制
- 查询失败时自动换Cookie重试（最多k个Cookie，可配置）
- 只有重试完所有Cookie都失败才认为查询真的失败
- 失败的Cookie会增加失败计数

### 3. 配置文件管理
- 统一的配置文件管理所有参数
- 支持线程数、批次大小、Cookie池配置等
- 易于调整和维护

## 配置说明

在 `config.py` 中可以配置以下参数：

```python
CRAWLER_CONFIG = {
    # 线程配置
    "threads": 40,              # 线程数量
    "batch_size": 80,           # 每批处理的数据量
    
    # Cookie池配置
    "cookie_pool": {
        "pool_size": 10,        # Cookie池大小
        "max_failures": 3,      # Cookie最大失败次数后删除
        "max_retries": 5,       # 查询最大重试次数（使用不同Cookie）
        "refresh_interval": 3600,  # Cookie刷新间隔（秒）
    },
    
    # 代理配置
    "proxy": {
        "enabled": True,
        "max_errors_before_change": 3,  # 更换代理前允许的最大错误次数
        "proxy_pool_size": 500,         # 代理池大小
    },
    
    # 搜索配置
    "search": {
        "max_pages": None,              # 最大爬取页数，None表示爬取所有页面
        "page_delay": (0.5, 1.5),      # 页面间延迟范围（秒）
        "request_timeout": 10,          # 请求超时时间（秒）
    }
}
```

## 使用方法

### 唯一入口：使用启动脚本
```bash
python run_crawler.py
```

**注意**: 请只使用 `run_crawler.py` 作为程序入口，不要直接运行 `crawler/main.py`

## 工作流程

1. **初始化阶段**
   - 读取配置文件
   - 初始化Cookie池（创建n个Cookie）
   - 启动后台Cookie刷新线程

2. **查询处理**
   - 从数据库获取未处理的查询
   - 分配给各个工作线程
   - 每个线程从Cookie池获取Cookie执行搜索

3. **重试机制**
   - 搜索失败时，释放当前Cookie并标记失败
   - 从Cookie池获取新Cookie重试
   - 最多重试k次（配置的max_retries）

4. **Cookie管理**
   - Cookie失败次数达到阈值时自动移除
   - 自动补充新Cookie到池中
   - 定期刷新过期Cookie

## 日志监控

程序会输出详细的日志信息，包括：
- Cookie池状态
- 查询进度
- 重试情况
- 错误信息

可以通过日志文件 `google_search.log` 查看详细运行情况。
