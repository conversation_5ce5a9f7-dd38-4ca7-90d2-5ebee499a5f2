# -*- coding: utf-8 -*-
"""
测试新的Cookie池管理
"""

import os
import json
import time

def test_cookie_pool():
    """测试Cookie池管理"""
    from crawler.cookies import CookiePool, CookieFileManager
    from crawler.config import get_cookie_config
    
    print("=== 测试Cookie池管理 ===")
    
    # 显示配置
    config = get_cookie_config()
    print(f"Cookie池配置: {config}")
    print(f"池大小: {config['pool_size']}")
    print(f"最大失败次数: {config['max_failures']}")
    
    # 测试CookieFileManager
    print("\n=== 测试CookieFileManager ===")
    test_manager = CookieFileManager(999)  # 使用999作为测试ID
    
    # 测试保存和加载
    test_cookies = {"test": "cookie_value", "session": "test_session"}
    test_manager.save_cookies(test_cookies)
    loaded_cookies = test_manager.load_cookies()
    print(f"保存的Cookie: {test_cookies}")
    print(f"加载的Cookie: {loaded_cookies}")
    print(f"Cookie匹配: {test_cookies == loaded_cookies}")
    
    # 测试失败计数
    print(f"\n初始失败次数: {test_manager.failure_count}")
    test_manager.mark_failure()
    print(f"标记失败后: {test_manager.failure_count}")
    test_manager.mark_failure()
    print(f"再次标记失败后: {test_manager.failure_count}")
    test_manager.mark_success()
    print(f"标记成功后: {test_manager.failure_count}")
    
    # 清理测试文件
    test_manager.delete_files()
    print("测试文件已清理")
    
    return True

def test_cookie_files():
    """测试Cookie文件结构"""
    from crawler.config import get_cookie_config
    
    print("\n=== 检查Cookie文件结构 ===")
    
    config = get_cookie_config()
    pool_size = config['pool_size']
    
    print(f"应该有 {pool_size} 个Cookie文件")
    
    # 检查cookies目录
    if not os.path.exists('cookies'):
        print("cookies目录不存在")
        return False
    
    # 列出现有的Cookie文件
    cookie_files = []
    meta_files = []
    
    for i in range(pool_size):
        cookie_file = f"cookies/pool_cookie_{i}.json"
        meta_file = f"cookies/pool_cookie_{i}_meta.json"
        
        if os.path.exists(cookie_file):
            cookie_files.append(i)
        if os.path.exists(meta_file):
            meta_files.append(i)
    
    print(f"现有Cookie文件: {cookie_files}")
    print(f"现有元数据文件: {meta_files}")
    
    # 显示元数据内容
    for i in meta_files[:3]:  # 只显示前3个
        meta_file = f"cookies/pool_cookie_{i}_meta.json"
        try:
            with open(meta_file, 'r') as f:
                meta = json.load(f)
            print(f"Cookie {i} 元数据: {meta}")
        except Exception as e:
            print(f"读取Cookie {i} 元数据失败: {e}")
    
    return True

def test_pool_status():
    """测试池状态"""
    try:
        from crawler.cookies import get_cookie_pool
        
        print("\n=== 测试Cookie池状态 ===")
        
        # 注意：这里不会实际创建Cookie，只是测试状态获取
        # 因为实际创建Cookie需要浏览器环境
        pool = get_cookie_pool()
        status = pool.get_pool_status()
        
        print(f"池状态: {status}")
        print(f"总数: {status['total']}")
        print(f"可用: {status['available']}")
        print(f"使用中: {status['in_use']}")
        print(f"过期: {status['expired']}")
        
        if 'detail' in status:
            print("\n详细状态:")
            for cookie_id, detail in status['detail'].items():
                print(f"  Cookie {cookie_id}: 失败{detail['failure_count']}次, 使用中:{detail['in_use']}, 过期:{detail['expired']}")
        
        return True
        
    except Exception as e:
        print(f"测试池状态失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 测试新的Cookie池管理系统...")
    
    success = True
    success &= test_cookie_pool()
    success &= test_cookie_files()
    success &= test_pool_status()
    
    if success:
        print("\n🎉 Cookie池管理测试通过！")
        print("\n新特性:")
        print("✅ 固定数量的Cookie文件")
        print("✅ 文件级别的失败次数记录")
        print("✅ 失败次数达到阈值时删除并重新创建")
        print("✅ 多线程安全的文件操作")
    else:
        print("\n❌ Cookie池管理测试失败！")
