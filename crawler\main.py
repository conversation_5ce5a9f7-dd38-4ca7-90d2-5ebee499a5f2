# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/3/19 20:30
File Name: main.py
"""
from spider import GoogleSearchThreadManager
from .proxy import ProxyManager
from config import get_config, get_logging_config
import time
import logging
from sqlalchemy import func
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db.db_models import SearchQueries
from db.init_db import SessionLocal

# Configure logging from config
log_config = get_logging_config()
logging.basicConfig(
    level=getattr(logging, log_config["level"]),
    format=log_config["format"],
    handlers=[
        logging.FileHandler(log_config["file"]),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('GoogleSearchMain')


def check_remaining_queries():
    """检查是否还有未完成的查询"""
    try:
        db = SessionLocal()
        try:
            count = db.query(func.count(SearchQueries.id)).filter(
                (SearchQueries.crawled_status.is_(None)) |
                (SearchQueries.crawled_status != 'completed')
            ).scalar()
            return count > 0
        finally:
            db.close()
    except Exception as e:
        logger.error(f"检查剩余查询失败: {e}")
        import traceback
        traceback.print_exc()
        return True  # 出错时假设还有查询待处理


def main():
    # 从配置文件读取参数
    config = get_config()
    threads = config["threads"]
    batch = config["batch_size"]

    logger.info("===== 谷歌搜索多线程工具 =====")
    logger.info(f"线程数量: {threads}")
    logger.info(f"批处理大小: {batch}")
    logger.info(f"Cookie池大小: {config['cookie_pool']['pool_size']}")
    logger.info(f"Cookie最大失败次数: {config['cookie_pool']['max_failures']}")
    logger.info(f"查询最大重试次数: {config['cookie_pool']['max_retries']}")
    logger.info("=" * 30)

    try:
        total_processed = 0

        # 持续运行直到所有查询都完成
        while check_remaining_queries():
            # 创建搜索管理器（使用配置文件中的参数）
            manager = GoogleSearchThreadManager()

            # 启动搜索
            logger.info("开始分配查询任务...")
            started = manager.start()

            # 如果有任务需要执行
            if started:
                logger.info("正在执行查询，请稍候...")
                manager.wait_completion()
                total_processed += manager.completed_queries
                logger.info(f"本批次查询已完成! 处理了 {manager.completed_queries} 个查询")
            else:
                logger.info("没有需要处理的查询，检查是否全部完成...")
                # 再次检查以确认是否所有查询都已完成
                if not check_remaining_queries():
                    logger.info("所有查询已完成!")
                    break
                else:
                    logger.info("仍有查询需要处理，但当前无法获取，稍后重试...")
                    time.sleep(5)  # 等待一段时间后重试

        logger.info(f"\n处理完成: 共处理了 {total_processed} 个查询")

    except Exception as e:
        logger.error(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
