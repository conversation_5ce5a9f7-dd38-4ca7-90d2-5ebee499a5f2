#!/bin/bash

# ����Playwright�������̺��ڴ�
echo "��ʼ�����������������..."

# installer.txt. ��������Chromium��ؽ���
pids=$(ps -ef | grep -E 'chromium|chrome|headless-shell|playwright' | grep -v grep | awk '{print $2}')

if [ -n "$pids" ]; then
    echo "���ֲ������̣�"
    ps -fp $pids

    # �ȳ���������ֹ
    echo "����������ֹ����..."
    kill -15 $pids 2>/dev/null
    sleep 3

    # ǿ����ֹδ�˳��Ľ���
    remaining=$(ps -ef | grep -E 'chromium|chrome|headless-shell|playwright' | grep -v grep | awk '{print $2}')
    if [ -n "$remaining" ]; then
        echo "ǿ����ֹ��̽���..."
        kill -9 $remaining 2>/dev/null
    fi
else
    echo "δ���ֲ�������"
fi

# 2. ����Playwright��ʱ�ļ�
echo "������ʱ�ļ�..."
find /tmp -name 'playwright*' -exec rm -rf {} \; 2>/dev/null

echo "������ɣ���ǰ�������̣�"
ps -ef | grep -E 'chromium|chrome|headless-shell|playwright' | grep -v grep