nohup python -u main.py >> log.log 2>&1 &
nohup python main.py > /dev/null 2>&1 &
tail -f log.log
ps aux | grep main.py
ps aux | grep run_cycle.sh
tail -100 log.log

nohup ./run_cycle.sh > /dev/null 2>&1 &


关于爬虫的代码是spider.py(爬虫代码)、proxy.py(获取代理)、main.py(主函数)、cookies.py(获取cookies)

你可以修改这几个文件，其他都不要改，因为与任务无关
请你讲这些文件移动到crawler文件夹下

现在我想修改一下逻辑
1. Cookie池管理
维护n个Cookie的池子
Cookie失败m次后才删除
2. 查询重试机制
查询失败时自动换Cookie重试（最多k个Cookie）
只有重试完所有Cookie都失败才认为查询真的失败
3.有一个配置文件来配置线程数，批次，等等